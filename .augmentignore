# .augmentignore - Additional files for Augment AI to ignore when indexing
# Note: .gitignore is still used, this is for additional exclusions

# Screenshot and mockup files (not needed for code understanding)
assets/screenshots/
*.mockup
*.excalidraw

# Package manager lock files (auto-generated, very large)
**/package-lock.json
**/yarn.lock
**/Brewfile.lock.json

# Script output files (temporary data)
scripts/*.json

# Workspace files
*.code-workspace
.gemini
