name: PR Tests

# Cancel redundant runs when new commits are pushed
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - main
    # Skip CI for draft PRs and docs-only changes
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.gitignore'
      - 'LICENSE'

jobs:
  test:
    name: Run Tests
    runs-on: macos-13 # ubuntu-latest-4-cores # must run with a runner with 8GB+ of RAM, ubuntu only has 7GB
    # Skip draft PRs
    if: github.event.pull_request.draft == false
    env:
      NODE_OPTIONS: "--max-old-space-size=8192"
    steps:
      - name: 🏗️ Checkout code
        uses: actions/checkout@v4
        with:
          # Only fetch the specific commit, not full history
          fetch-depth: 1

      - name: 🏗️ Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
          cache-dependency-path: |
            package-lock.json
            functions/package-lock.json
            functions/report-generation/package-lock.json
            functions/swagger-docs/package-lock.json
            web-sign-up/package-lock.json

      - name: 📦 Cache node_modules
        uses: actions/cache@v4
        id: cache-node-modules
        with:
          path: |
            node_modules
            functions/node_modules
            functions/report-generation/node_modules
            functions/swagger-docs/node_modules
            web-sign-up/node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('package-lock.json', 'functions/package-lock.json', 'functions/report-generation/package-lock.json', 'functions/swagger-docs/package-lock.json', 'web-sign-up/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: 📦 Install dependencies (all modules)
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: npm run ci:all

      - name: 🧪 Run tests
        run: npm run test:ci
        env:
          # Reduce Jest workers to prevent memory issues
          NODE_OPTIONS: "--max-old-space-size=8192"
